"""
نظام طباعة متقدم للفواتير مع رسم مباشر بالألوان
"""

import os
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton, 
                             QLabel, QRadioButton, QButtonGroup, QMessageBox,
                             QSplitter, QFrame, QScrollArea, QWidget)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import (QFont, QIcon, QPainter, QPen, QBrush, QColor, 
                         QLinearGradient, QPixmap, QFontMetrics, QImage)
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
from sqlalchemy.orm import sessionmaker
from database.models import Transaction, Customer, TransactionItem, Product, Supplier, TransactionType
from utils.company_settings import get_company_settings
from utils.currency_formatter import format_currency, format_number
from main import resource_path
import json
import base64

class AdvancedInvoicePrinter(QDialog):
    def __init__(self, engine, invoice_id, parent=None):
        super().__init__(parent)
        self.engine = engine
        self.invoice_id = invoice_id
        self.invoice_data = None
        self.printer_type = "a4"  # افتراضي A4
        
        self.setWindowTitle("طباعة الفاتورة - تصميم متقدم")
        self.setGeometry(100, 100, 1000, 700)
        self.setModal(True)
        
        self.setup_ui()
        self.load_invoice_data()
        self.update_preview()
    
    def setup_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # عنوان النافذة
        title_label = QLabel("🎨 طباعة الفاتورة - تصميم متقدم")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                font-size: 20px;
                font-weight: bold;
                padding: 15px;
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title_label)
        
        # إعدادات الطابعة
        printer_frame = QFrame()
        printer_frame.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border: 2px solid #DEE2E6;
                border-radius: 8px;
                padding: 10px;
                margin: 5px;
            }
        """)
        printer_layout = QHBoxLayout()
        printer_frame.setLayout(printer_layout)
        
        printer_label = QLabel("🖨️ نوع الطابعة:")
        printer_label.setFont(QFont("Arial", 12, QFont.Bold))
        printer_layout.addWidget(printer_label)
        
        self.printer_group = QButtonGroup()
        
        self.a4_radio = QRadioButton("📄 طابعة A4")
        self.a4_radio.setChecked(True)
        self.a4_radio.toggled.connect(self.on_printer_type_changed)
        self.printer_group.addButton(self.a4_radio)
        printer_layout.addWidget(self.a4_radio)
        
        self.roll_radio = QRadioButton("📜 طابعة رول (80مم)")
        self.roll_radio.toggled.connect(self.on_printer_type_changed)
        self.printer_group.addButton(self.roll_radio)
        printer_layout.addWidget(self.roll_radio)
        
        printer_layout.addStretch()
        layout.addWidget(printer_frame)
        
        # منطقة المعاينة
        self.preview_scroll = QScrollArea()
        self.preview_widget = QWidget()
        self.preview_scroll.setWidget(self.preview_widget)
        self.preview_scroll.setWidgetResizable(True)
        self.preview_scroll.setStyleSheet("""
            QScrollArea {
                border: 2px solid #DEE2E6;
                border-radius: 8px;
                background-color: white;
            }
        """)
        layout.addWidget(self.preview_scroll)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        self.preview_btn = QPushButton("👁️ معاينة")
        self.preview_btn.setStyleSheet(self.get_button_style("#17A2B8"))
        self.preview_btn.clicked.connect(self.update_preview)
        buttons_layout.addWidget(self.preview_btn)
        
        self.pdf_btn = QPushButton("💾 حفظ PDF")
        self.pdf_btn.setStyleSheet(self.get_button_style("#28A745"))
        self.pdf_btn.clicked.connect(self.save_as_pdf)
        buttons_layout.addWidget(self.pdf_btn)
        
        self.print_btn = QPushButton("🖨️ طباعة")
        self.print_btn.setStyleSheet(self.get_button_style("#007BFF"))
        self.print_btn.clicked.connect(self.print_invoice)
        buttons_layout.addWidget(self.print_btn)
        
        self.close_btn = QPushButton("❌ إغلاق")
        self.close_btn.setStyleSheet(self.get_button_style("#DC3545"))
        self.close_btn.clicked.connect(self.close)
        buttons_layout.addWidget(self.close_btn)
        
        layout.addLayout(buttons_layout)
    
    def get_button_style(self, color):
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                font-size: 24px;
                font-weight: bold;
                padding: 12px 20px;
                border: none;
                border-radius: 6px;
                min-width: 120px;
                margin: 5px;
            }}
            QPushButton:hover {{
                background-color: {color}DD;
            }}
            QPushButton:pressed {{
                background-color: {color}BB;
            }}
        """
    
    def on_printer_type_changed(self):
        if self.a4_radio.isChecked():
            self.printer_type = "a4"
        else:
            self.printer_type = "roll"
        self.update_preview()
    
    def load_invoice_data(self):
        """تحميل بيانات الفاتورة أو المرتجع"""
        try:
            Session = sessionmaker(bind=self.engine)
            session = Session()

            # تحميل الفاتورة أو المرتجع
            transaction = session.query(Transaction).filter_by(id=self.invoice_id).first()
            if not transaction:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على الفاتورة")
                return

            # تحديد نوع المعاملة
            is_return = transaction.type in [TransactionType.SALE_RETURN, TransactionType.PURCHASE_RETURN]
            is_sale_return = transaction.type == TransactionType.SALE_RETURN
            is_purchase_return = transaction.type == TransactionType.PURCHASE_RETURN
            is_purchase = transaction.type == TransactionType.PURCHASE
            is_sale = transaction.type == TransactionType.SALE

            # تحميل العميل أو المورد
            contact_name = "غير محدد"
            if is_sale_return and transaction.customer_id:
                customer = session.query(Customer).filter_by(id=transaction.customer_id).first()
                contact_name = customer.name if customer else "عميل نقدي"
            elif is_purchase_return and transaction.supplier_id:
                supplier = session.query(Supplier).filter_by(id=transaction.supplier_id).first()
                contact_name = supplier.name if supplier else "غير محدد"
            elif is_purchase and transaction.supplier_id:
                supplier = session.query(Supplier).filter_by(id=transaction.supplier_id).first()
                contact_name = supplier.name if supplier else "غير محدد"
            elif is_sale and transaction.customer_id:
                customer = session.query(Customer).filter_by(id=transaction.customer_id).first()
                contact_name = customer.name if customer else "عميل نقدي"

            # تحميل المنتجات
            items = session.query(TransactionItem, Product).join(Product).filter(
                TransactionItem.transaction_id == self.invoice_id
            ).all()

            items_data = []
            for item, product in items:
                # حساب الإجمالي
                total_price = float(item.quantity) * float(item.price)
                items_data.append({
                    'name': product.name or 'غير محدد',
                    'quantity': float(item.quantity or 0),
                    'price': float(item.price or 0),
                    'total': total_price,
                    'unit': product.unit or 'قطعة'
                })

            # تحديد عنوان الفاتورة
            if is_sale_return:
                invoice_title = "فاتورة مرتجع مبيعات"
                invoice_type = "مرتجع مبيعات"
            elif is_purchase_return:
                invoice_title = "فاتورة مرتجع مشتريات"
                invoice_type = "مرتجع مشتريات"
            elif is_purchase:
                invoice_title = "فاتورة مشتريات"
                invoice_type = "مشتريات"
            else:
                invoice_title = "فاتورة مبيعات"
                invoice_type = "مبيعات"

            # معلومات الفاتورة الأصلية للمرتجعات
            original_invoice_id = None
            if is_return and transaction.original_transaction_id:
                original_invoice_id = transaction.original_transaction_id

            self.invoice_data = {
                'id': transaction.id,
                'date': transaction.date.strftime("%Y-%m-%d") if transaction.date else "غير محدد",
                'customer_name': contact_name,
                'items': items_data,
                'total_amount': float(transaction.total_amount or 0),
                'paid_amount': float(transaction.paid_amount or 0),
                'remaining_amount': float((transaction.total_amount or 0) - (transaction.paid_amount or 0)),
                'discount': float(transaction.discount or 0),
                'is_return': is_return,
                'is_sale_return': is_sale_return,
                'is_purchase_return': is_purchase_return,
                'invoice_title': invoice_title,
                'invoice_type': invoice_type,
                'original_invoice_id': original_invoice_id
            }

            session.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل بيانات الفاتورة:\n{str(e)}")
    
    def update_preview(self):
        """تحديث المعاينة برسم مباشر"""
        if not self.invoice_data:
            return
        
        # إنشاء صورة للمعاينة
        if self.printer_type == "a4":
            width, height = 800, 1100
        else:
            width, height = 300, 800
        
        pixmap = QPixmap(width, height)
        pixmap.fill(QColor(255, 255, 255))  # خلفية بيضاء
        
        painter = QPainter(pixmap)
        self.draw_invoice(painter, width, height)
        painter.end()
        
        # عرض الصورة في المعاينة
        preview_label = QLabel()
        preview_label.setPixmap(pixmap)
        preview_label.setAlignment(Qt.AlignCenter)

        # تحديث widget المعاينة
        # إزالة التخطيط القديم إن وجد
        old_layout = self.preview_widget.layout()
        if old_layout:
            while old_layout.count():
                child = old_layout.takeAt(0)
                if child.widget():
                    child.widget().deleteLater()
            old_layout.deleteLater()

        layout = QVBoxLayout()
        layout.addWidget(preview_label)
        self.preview_widget.setLayout(layout)
    
    def draw_invoice(self, painter, width, height):
        """رسم الفاتورة بالألوان والتصميم الجميل"""
        painter.setRenderHint(QPainter.Antialiasing)

        # التحقق من وجود بيانات الفاتورة
        if not self.invoice_data or not self.invoice_data.get('items'):
            painter.setPen(QPen(QColor(255, 0, 0), 2))
            painter.setFont(QFont("Arial", 16, QFont.Bold))
            painter.drawText(50, height//2, "خطأ: لا توجد بيانات للفاتورة")
            return

        # تحميل إعدادات الشركة
        company_settings = get_company_settings()
        company_name = company_settings.get("company_name", "هوم سنتر للأدوات المنزلية")
        company_address = company_settings.get("address", "حي الصفا - جدة - المملكة العربية السعودية")
        company_phone = company_settings.get("phone", "01010101010")
        company_email = company_settings.get("email", "<EMAIL>")

        y = 20

        if self.printer_type == "a4":
            y = self.draw_a4_invoice(painter, width, height, company_name, company_address, company_phone, company_email)
        else:
            y = self.draw_roll_invoice(painter, width, height, company_name, company_phone)
    
    def draw_a4_invoice(self, painter, width, height, company_name, company_address, company_phone, company_email):
        """رسم فاتورة A4 بتصميم جميل مع نصوص واضحة ولوجو مخصص"""
        y = 30

        # رسم الترويسة بالأبيض والأسود مع الحفاظ على التصميم
        header_height = 180  # زيادة الارتفاع من 120 إلى 180

        # خلفية بيضاء مع حدود سوداء
        painter.setBrush(QBrush(QColor(255, 255, 255)))  # خلفية بيضاء
        painter.setPen(QPen(QColor(0, 0, 0), 2))  # حدود سوداء
        painter.drawRoundedRect(30, y, width - 60, header_height, 12, 12)

        # رسم اللوجو المخصص أو الافتراضي مع حجم أكبر
        logo_x = 50
        logo_y = y + 20
        logo_size = 120  # زيادة حجم اللوجو من 90 إلى 120

        # محاولة تحميل اللوجو المحفوظ
        logo_loaded = False
        try:
            # قراءة إعدادات الشركة مباشرة من الملف
            settings_file = "company_settings.json"
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    logo_path = settings.get('logo_path', '')
                    logo_base64 = settings.get('logo_base64', '')

                    if logo_base64:
                        # تحميل اللوجو من base64
                        image = QImage()
                        image.loadFromData(base64.b64decode(logo_base64))
                        logo_pixmap = QPixmap.fromImage(image)
                        if not logo_pixmap.isNull():
                            scaled_logo = logo_pixmap.scaled(logo_size, logo_size, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                            painter.drawPixmap(logo_x, logo_y, scaled_logo)
                            logo_loaded = True
                    elif logo_path and os.path.exists(logo_path):
                        # تحميل اللوجو من المسار
                        logo_pixmap = QPixmap(logo_path)
                        if not logo_pixmap.isNull():
                            scaled_logo = logo_pixmap.scaled(logo_size, logo_size, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                            painter.drawPixmap(logo_x, logo_y, scaled_logo)
                            logo_loaded = True

        except Exception as e:
            print(f"خطأ في تحميل اللوجو: {e}")

        # إذا لم يتم تحميل اللوجو المخصص، استخدم الافتراضي
        if not logo_loaded:
            try:
                # محاولة تحميل اللوجو الافتراضي
                default_logo_path = resource_path(os.path.join('assets', 'company01_logo.png'))
                if os.path.exists(default_logo_path):
                    logo_pixmap = QPixmap(default_logo_path)
                    if not logo_pixmap.isNull():
                        scaled_logo = logo_pixmap.scaled(logo_size, logo_size, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                        painter.drawPixmap(logo_x, logo_y, scaled_logo)
                        logo_loaded = True
            except Exception as e:
                print(f"خطأ في تحميل اللوجو الافتراضي: {e}")

        # إذا فشل كل شيء، استخدم النص الافتراضي
        if not logo_loaded:
            painter.setPen(QPen(QColor(0, 0, 0), 2))
            painter.setFont(QFont("Arial", 32, QFont.Bold))
            painter.drawText(logo_x, logo_y + 50, "🏢")
            painter.setFont(QFont("Arial", 16, QFont.Bold))
            painter.drawText(logo_x, logo_y + 80, company_name)

        # معلومات الشركة مع مساحة محسوبة ومحسنة مع الارتفاع الجديد
        # تحديد المساحة المتاحة للنص (بعد اللوجو مع مسافة)
        company_start_x = logo_x + logo_size + 40  # بداية النص بعد اللوجو + مسافة
        company_width = (width - 60) - company_start_x  # المساحة المتبقية
        company_x = company_start_x

        painter.setPen(QPen(QColor(0, 0, 0), 2))  # نص أسود

        # رسم اسم الشركة مع خط أكبر
        painter.setFont(QFont("Arial", 18, QFont.Bold))  # خط أكبر

        # تقسيم اسم الشركة بناءً على العرض المتاح
        name_lines = []
        words = company_name.split()
        current_line = ""

        for word in words:
            test_line = current_line + " " + word if current_line else word
            # قياس عرض النص الفعلي
            font_metrics = painter.fontMetrics()
            text_width = font_metrics.width(test_line)

            if text_width <= company_width - 30:  # هامش أمان 30px
                current_line = test_line
            else:
                if current_line:
                    name_lines.append(current_line)
                    current_line = word
                else:
                    # إذا كانت الكلمة الواحدة طويلة جداً، قسمها
                    if len(word) > 15:
                        name_lines.append(word[:15] + "...")
                        current_line = ""
                    else:
                        name_lines.append(word)
                        current_line = ""

        if current_line:
            name_lines.append(current_line)

        # رسم اسم الشركة مع إحداثيات صحيحة
        name_start_y = y + 50  # بداية من داخل الترويسة
        line_height = 22  # ارتفاع مناسب للأسطر

        for i, line in enumerate(name_lines):
            line_y = name_start_y + (i * line_height)
            # التأكد من أن السطر داخل حدود الترويسة
            if line_y <= y + header_height - 30:
                # حساب موضع النص للمحاذاة اليمنى
                font_metrics = painter.fontMetrics()
                text_width = font_metrics.width(line)
                text_x = company_x + company_width - text_width - 10  # محاذاة يمنى مع هامش
                painter.drawText(text_x, line_y, line)

        # تحديد موقع بداية التفاصيل مع مساحة أكبر
        details_start_y = name_start_y + (len(name_lines) * line_height) + 15

        # تفاصيل الشركة مع خط أكبر ومساحة أكبر
        painter.setFont(QFont("Arial", 13))  # خط أكبر للتفاصيل
        detail_height = 20  # ارتفاع أكبر للأسطر
        current_y = details_start_y

        # حساب المساحة المتبقية في الترويسة الجديدة الأكبر
        remaining_height = (y + header_height - 20) - current_y
        max_lines = max(1, remaining_height // detail_height)

        # العنوان مع إمكانية عرض أكثر
        if company_address and max_lines > 0:
            font_metrics = painter.fontMetrics()

            # تقسيم العنوان إلى أسطر متعددة إذا كان طويل
            if len(company_address) > 50:
                # تقسيم العنوان إلى جزئين
                if ' - ' in company_address:
                    parts = company_address.split(' - ')
                    # السطر الأول
                    first_part = f"📍 {parts[0]}"
                    if current_y <= y + header_height - 40:
                        # محاذاة يمنى للعنوان
                        text_width = font_metrics.width(first_part)
                        text_x = company_x + company_width - text_width - 10
                        painter.drawText(text_x, current_y, first_part)
                        current_y += detail_height
                        max_lines -= 1

                    # السطر الثاني
                    if len(parts) > 1 and max_lines > 0 and current_y <= y + header_height - 40:
                        second_part = f"   {' - '.join(parts[1:])}"
                        if len(second_part) > 50:
                            second_part = second_part[:47] + "..."
                        # محاذاة يمنى للعنوان
                        text_width = font_metrics.width(second_part)
                        text_x = company_x + company_width - text_width - 10
                        painter.drawText(text_x, current_y, second_part)
                        current_y += detail_height
                        max_lines -= 1
                else:
                    # تقسيم العنوان بدون فواصل
                    words = company_address.split()
                    mid = len(words) // 2
                    first_line = f"📍 {' '.join(words[:mid])}"
                    second_line = f"   {' '.join(words[mid:])}"

                    # رسم السطر الأول
                    if current_y <= y + header_height - 40:
                        text_width = font_metrics.width(first_line)
                        text_x = company_x + company_width - text_width - 10
                        painter.drawText(text_x, current_y, first_line)
                        current_y += detail_height
                        max_lines -= 1

                    # رسم السطر الثاني
                    if max_lines > 0 and current_y <= y + header_height - 40:
                        text_width = font_metrics.width(second_line)
                        text_x = company_x + company_width - text_width - 10
                        painter.drawText(text_x, current_y, second_line)
                        current_y += detail_height
                        max_lines -= 1
            else:
                # عنوان قصير
                address_text = f"📍 {company_address}"
                if current_y <= y + header_height - 40:
                    text_width = font_metrics.width(address_text)
                    text_x = company_x + company_width - text_width - 10
                    painter.drawText(text_x, current_y, address_text)
                    current_y += detail_height
                    max_lines -= 1

        # إضافة مسافة إضافية قبل الهاتف
        current_y += 5  # مسافة إضافية

        # الهاتف
        if company_phone and max_lines > 0 and current_y <= y + header_height - 40:
            phone_text = f"📞 {company_phone}"
            font_metrics = painter.fontMetrics()
            text_width = font_metrics.width(phone_text)
            text_x = company_x + company_width - text_width - 10
            painter.drawText(text_x, current_y, phone_text)
            current_y += detail_height

        # البريد الإلكتروني
        if company_email and max_lines > 0 and current_y <= y + header_height - 40:
            email_text = f"📧 {company_email}"
            font_metrics = painter.fontMetrics()
            text_width = font_metrics.width(email_text)

            if text_width <= company_width - 20:
                text_x = company_x + company_width - text_width - 10
                painter.drawText(text_x, current_y, email_text)
            else:
                # تقصير البريد الإلكتروني إذا كان طويل
                if len(company_email) > 30:
                    short_email = company_email[:27] + "..."
                    short_text = f"📧 {short_email}"
                    text_width = font_metrics.width(short_text)
                    text_x = company_x + company_width - text_width - 10
                    painter.drawText(text_x, current_y, short_text)
                else:
                    text_x = company_x + company_width - text_width - 10
                    painter.drawText(text_x, current_y, email_text)
            current_y += detail_height

        # الرقم الضريبي
        if max_lines > 0 and current_y <= y + header_height - 40:
            # الحصول على الرقم الضريبي من إعدادات الشركة
            try:
                tax_number = "310123456789003"  # رقم افتراضي

                tax_text = f"🏢 ض.ب: {tax_number}"
                font_metrics = painter.fontMetrics()
                text_width = font_metrics.width(tax_text)
                text_x = company_x + company_width - text_width - 10
                painter.drawText(text_x, current_y, tax_text)
                current_y += detail_height

            except Exception as e:
                print(f"خطأ في قراءة الرقم الضريبي: {e}")
                # رقم ضريبي افتراضي
                tax_text = "🏢 ض.ب: 310123456789003"
                font_metrics = painter.fontMetrics()
                text_width = font_metrics.width(tax_text)
                text_x = company_x + company_width - text_width - 10
                painter.drawText(text_x, current_y, tax_text)

        y += header_height + 20
        
        # عنوان الفاتورة بالأبيض والأسود مع الحفاظ على التصميم
        title_height = 60

        # خلفية بيضاء مع حدود سوداء
        painter.setBrush(QBrush(QColor(255, 255, 255)))
        painter.setPen(QPen(QColor(0, 0, 0), 2))
        painter.drawRoundedRect(30, y, width - 60, title_height, 10, 10)

        # عرض عنوان الفاتورة مع لون مميز للمرتجعات
        if self.invoice_data.get('is_return', False):
            if self.invoice_data.get('is_sale_return', False):
                painter.setPen(QPen(QColor(231, 76, 60), 2))  # أحمر للمرتجع
            else:
                painter.setPen(QPen(QColor(155, 89, 182), 2))  # بنفسجي للمرتجع
        else:
            painter.setPen(QPen(QColor(0, 0, 0), 2))  # نص أسود للفواتير العادية

        painter.setFont(QFont("Arial", 20, QFont.Bold))

        # عنوان الفاتورة الديناميكي
        title_text = f"🧾 {self.invoice_data.get('invoice_title', 'فاتورة')}"
        painter.drawText(30, y, width - 60, title_height, Qt.AlignCenter, title_text)

        y += title_height + 20

        # رقم الفاتورة/المرتجع ورقم الفاتورة الأصلية
        info_height = 40

        # خلفية بيضاء مع حدود سوداء
        painter.setBrush(QBrush(QColor(255, 255, 255)))
        painter.setPen(QPen(QColor(0, 0, 0), 1))
        painter.drawRoundedRect(30, y, width - 60, info_height, 8, 8)

        painter.setPen(QPen(QColor(44, 62, 80), 1))
        painter.setFont(QFont("Arial", 14, QFont.Bold))

        # رقم الفاتورة/المرتجع
        if self.invoice_data.get('is_return', False):
            number_text = f"رقم المرتجع: R{self.invoice_data['id']:06d}"
        else:
            number_text = f"رقم الفاتورة: #{self.invoice_data['id']:06d}"

        painter.drawText(30, y, width - 60, info_height, Qt.AlignCenter, number_text)
        y += info_height + 10

        # رقم الفاتورة الأصلية للمرتجعات
        if self.invoice_data.get('is_return', False) and self.invoice_data.get('original_invoice_id'):
            original_height = 35

            # خلفية بيضاء مع حدود زرقاء
            painter.setBrush(QBrush(QColor(255, 255, 255)))
            painter.setPen(QPen(QColor(52, 152, 219), 1))
            painter.drawRoundedRect(30, y, width - 60, original_height, 8, 8)

            painter.setPen(QPen(QColor(52, 152, 219), 1))
            painter.setFont(QFont("Arial", 12, QFont.Bold))
            original_text = f"الفاتورة الأصلية: #{self.invoice_data['original_invoice_id']:06d}"
            painter.drawText(30, y, width - 60, original_height, Qt.AlignCenter, original_text)
            y += original_height + 10

        y += 10  # مسافة إضافية

        # جدول المنتجات مع تدرج أخضر ومساحة محسوبة
        table_y = y
        table_width = width - 60  # مساحة أكبر للجدول
        table_x = 30

        # رأس الجدول بالأبيض والأسود مع ارتفاع أكبر
        header_height = 50

        # خلفية بيضاء مع حدود سوداء
        painter.setBrush(QBrush(QColor(255, 255, 255)))
        painter.setPen(QPen(QColor(0, 0, 0), 2))
        painter.drawRoundedRect(table_x, table_y, table_width, header_height, 8, 8)

        # عناوين الأعمدة مع توزيع أفضل للمساحة
        painter.setPen(QPen(QColor(0, 0, 0), 2))  # نص أسود
        painter.setFont(QFont("Arial", 14, QFont.Bold))

        # توزيع عرض الأعمدة بشكل أفضل
        col1_width = int(table_width * 0.20)  # الإجمالي - 20%
        col2_width = int(table_width * 0.20)  # الوحدة - 20%
        col3_width = int(table_width * 0.20)  # السعر - 20%
        col4_width = int(table_width * 0.15)  # الكمية - 15%
        col5_width = table_width - col1_width - col2_width - col3_width - col4_width  # المنتج - الباقي

        # رسم عناوين الأعمدة مع مساحة كافية - الترتيب: الإجمالي | الوحدة | السعر | الكمية | المنتج
        painter.drawText(table_x, table_y, col1_width, header_height, Qt.AlignCenter, "الإجمالي")
        painter.drawText(table_x + col1_width, table_y, col2_width, header_height, Qt.AlignCenter, "الوحدة")
        painter.drawText(table_x + col1_width + col2_width, table_y, col3_width, header_height, Qt.AlignCenter, "السعر")
        painter.drawText(table_x + col1_width + col2_width + col3_width, table_y, col4_width, header_height, Qt.AlignCenter, "الكمية")
        painter.drawText(table_x + col1_width + col2_width + col3_width + col4_width, table_y, col5_width, header_height, Qt.AlignCenter, "المنتج")

        table_y += header_height

        # صفوف المنتجات مع ارتفاع أكبر
        painter.setFont(QFont("Arial", 12))
        row_height = 45

        for i, item in enumerate(self.invoice_data['items']):
            # خلفية متناوبة بالأبيض والرمادي الفاتح
            if i % 2 == 0:
                painter.setBrush(QBrush(QColor(245, 245, 245)))  # رمادي فاتح جداً
            else:
                painter.setBrush(QBrush(QColor(255, 255, 255)))  # أبيض

            painter.setPen(QPen(QColor(0, 0, 0), 1))  # حدود سوداء
            painter.drawRect(table_x, table_y, table_width, row_height)

            # النصوص مع مساحة كافية ومحاذاة صحيحة
            painter.setPen(QPen(QColor(0, 0, 0), 1))  # نص أسود

            # الترتيب: الإجمالي | الوحدة | السعر | الكمية | المنتج
            painter.drawText(table_x, table_y, col1_width, row_height, Qt.AlignCenter, format_number(float(item.get('total', 0))))
            painter.drawText(table_x + col1_width, table_y, col2_width, row_height, Qt.AlignCenter, str(item.get('unit', 'قطعة')))
            painter.drawText(table_x + col1_width + col2_width, table_y, col3_width, row_height, Qt.AlignCenter, format_number(float(item.get('price', 0))))
            painter.drawText(table_x + col1_width + col2_width + col3_width, table_y, col4_width, row_height, Qt.AlignCenter, str(item.get('quantity', 0)))
            # اسم المنتج مع تقليم النص إذا كان طويل
            product_name = str(item.get('name', 'غير محدد'))
            if len(product_name) > 25:
                product_name = product_name[:22] + "..."
            painter.drawText(table_x + col1_width + col2_width + col3_width + col4_width, table_y, col5_width, row_height, Qt.AlignVCenter | Qt.AlignRight, product_name)

            table_y += row_height
        
        table_y += 20
        
        # تفاصيل الفاتورة بالأبيض والأسود مع مساحة محسوبة
        details_height = 180

        # خلفية بيضاء مع حدود سوداء
        painter.setBrush(QBrush(QColor(255, 255, 255)))
        painter.setPen(QPen(QColor(0, 0, 0), 2))
        painter.drawRoundedRect(table_x, table_y, table_width, details_height, 12, 12)

        # عنوان التفاصيل مع ارتفاع أكبر
        title_height = 50

        # خلفية بيضاء مع حدود سوداء للعنوان
        painter.setBrush(QBrush(QColor(255, 255, 255)))
        painter.setPen(QPen(QColor(0, 0, 0), 2))
        painter.drawRoundedRect(table_x, table_y, table_width, title_height, 12, 12)

        painter.setPen(QPen(QColor(0, 0, 0), 2))  # نص أسود
        painter.setFont(QFont("Arial", 16, QFont.Bold))
        painter.drawText(table_x, table_y, table_width, title_height, Qt.AlignCenter, "📋 تفاصيل الفاتورة")

        # التفاصيل مع مساحة أكبر وتوزيع أفضل
        painter.setPen(QPen(QColor(0, 0, 0), 1))  # نص أسود
        painter.setFont(QFont("Arial", 13))

        detail_y = table_y + title_height + 15
        left_x = table_x + 20
        right_x = table_x + table_width // 2 + 20

        # الجانب الأيسر - معلومات أساسية
        painter.drawText(left_x, detail_y, 300, 25, Qt.AlignVCenter, f"📅 التاريخ: {self.invoice_data['date']}")

        # تحديد تسمية العميل/المورد
        contact_label = "العميل"
        if self.invoice_data.get('is_purchase_return', False) or self.invoice_data.get('invoice_type') == 'مشتريات':
            contact_label = "المورد"

        painter.drawText(left_x, detail_y + 30, 300, 25, Qt.AlignVCenter, f"👤 {contact_label}: {self.invoice_data['customer_name']}")

        # حالة الفاتورة
        status = "مدفوعة" if self.invoice_data['remaining_amount'] == 0 else "جزئية" if self.invoice_data['paid_amount'] > 0 else "غير مدفوعة"
        status_icon = "✅" if status == "مدفوعة" else "⚠️" if status == "جزئية" else "❌"
        painter.drawText(left_x, detail_y + 60, 300, 25, Qt.AlignVCenter, f"{status_icon} الحالة: {status}")

        # الجانب الأيمن - المبالغ المالية
        subtotal = sum(float(item.get('total', 0)) for item in self.invoice_data['items'])
        painter.drawText(right_x, detail_y, 300, 25, Qt.AlignVCenter, f"💰 المجموع الفرعي: {format_currency(subtotal)}")
        painter.drawText(right_x, detail_y + 30, 300, 25, Qt.AlignVCenter, f"💳 المدفوع: {format_currency(self.invoice_data['paid_amount'])}")

        # المبلغ المتبقي (يظهر دائماً)
        painter.drawText(right_x, detail_y + 60, 300, 25, Qt.AlignVCenter, f"⏳ المتبقي: {format_currency(self.invoice_data['remaining_amount'])}")

        # معلومات إضافية إذا وجدت
        extra_y = detail_y + 90
        if self.invoice_data['discount'] > 0:
            painter.drawText(left_x, extra_y, 300, 25, Qt.AlignVCenter, f"🎯 الخصم: {format_currency(self.invoice_data['discount'])}")

        # الإجمالي النهائي منفصل عن قسم التفاصيل
        total_y = table_y + details_height + 20  # مسافة كافية بعد قسم التفاصيل

        # خلفية بيضاء مع حدود سوداء سميكة
        painter.setBrush(QBrush(QColor(255, 255, 255)))
        painter.setPen(QPen(QColor(0, 0, 0), 3))  # حدود سوداء سميكة
        painter.drawRoundedRect(table_x + 20, total_y, table_width - 40, 50, 8, 8)  # ارتفاع أكبر

        painter.setPen(QPen(QColor(0, 0, 0), 2))  # نص أسود
        painter.setFont(QFont("Arial", 18, QFont.Bold))  # خط أكبر
        painter.drawText(table_x + 20, total_y, table_width - 40, 50, Qt.AlignCenter, f"💎 الإجمالي النهائي: {self.invoice_data['total_amount']:,.0f} ج.م")

        return total_y + 70  # مسافة كافية بعد الإجمالي

    def draw_roll_invoice(self, painter, width, height, company_name, company_phone):
        """رسم فاتورة رول بتصميم محسن مع جميع التفاصيل"""
        y = 10

        # تحميل إعدادات الشركة الكاملة
        company_settings = get_company_settings()
        company_address = company_settings.get("address", "حي الصفا - جدة - المملكة العربية السعودية")
        company_email = company_settings.get("email", "<EMAIL>")

        # ترويسة محسنة بالأبيض والأسود مع ارتفاع أكبر (بدون حدود)
        header_height = 120  # ارتفاع أكبر للترويسة

        # رسم اللوجو المخصص أو الافتراضي (حجم أصغر للرول)
        logo_x = 10
        logo_y = y + 10
        logo_size = 60  # حجم مناسب للرول

        # محاولة تحميل اللوجو المحفوظ
        logo_loaded = False
        try:
            # قراءة إعدادات الشركة مباشرة من الملف
            settings_file = "company_settings.json"
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    logo_path = settings.get('logo_path', '')
                    logo_base64 = settings.get('logo_base64', '')

                    if logo_base64:
                        # تحميل اللوجو من base64
                        image = QImage()
                        image.loadFromData(base64.b64decode(logo_base64))
                        logo_pixmap = QPixmap.fromImage(image)
                        if not logo_pixmap.isNull():
                            scaled_logo = logo_pixmap.scaled(logo_size, logo_size, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                            painter.drawPixmap(logo_x, logo_y, scaled_logo)
                            logo_loaded = True
                    elif logo_path and os.path.exists(logo_path):
                        # تحميل اللوجو من المسار
                        logo_pixmap = QPixmap(logo_path)
                        if not logo_pixmap.isNull():
                            scaled_logo = logo_pixmap.scaled(logo_size, logo_size, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                            painter.drawPixmap(logo_x, logo_y, scaled_logo)
                            logo_loaded = True

        except Exception as e:
            print(f"خطأ في تحميل اللوجو: {e}")

        # إذا لم يتم تحميل اللوجو المخصص، استخدم الافتراضي
        if not logo_loaded:
            try:
                # محاولة تحميل اللوجو الافتراضي
                default_logo_path = resource_path(os.path.join('assets', 'company01_logo.png'))
                if os.path.exists(default_logo_path):
                    logo_pixmap = QPixmap(default_logo_path)
                    if not logo_pixmap.isNull():
                        scaled_logo = logo_pixmap.scaled(logo_size, logo_size, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                        painter.drawPixmap(logo_x, logo_y, scaled_logo)
                        logo_loaded = True
            except Exception as e:
                print(f"خطأ في تحميل اللوجو الافتراضي: {e}")

        # إذا فشل كل شيء، استخدم النص الافتراضي
        if not logo_loaded:
            painter.setPen(QPen(QColor(0, 0, 0), 2))
            painter.setFont(QFont("Arial", 16, QFont.Bold))
            painter.drawText(logo_x, logo_y + 25, "🏢")
            painter.setFont(QFont("Arial", 10, QFont.Bold))
            painter.drawText(logo_x, logo_y + 40, company_name)

        # معلومات الشركة في الرول (بجانب اللوجو)
        company_start_x = logo_x + logo_size + 15  # بداية النص بعد اللوجو
        company_width = width - company_start_x - 10  # المساحة المتبقية

        painter.setPen(QPen(QColor(0, 0, 0), 2))  # نص أسود

        # اسم الشركة مع خط مناسب للرول
        painter.setFont(QFont("Arial", 11, QFont.Bold))

        # تقسيم اسم الشركة للرول
        name_lines = []
        words = company_name.split()
        current_line = ""

        for word in words:
            test_line = current_line + " " + word if current_line else word
            font_metrics = painter.fontMetrics()
            text_width = font_metrics.width(test_line)

            if text_width <= company_width - 10:  # هامش أمان للرول
                current_line = test_line
            else:
                if current_line:
                    name_lines.append(current_line)
                    current_line = word
                else:
                    # تقصير الكلمة الطويلة للرول
                    if len(word) > 12:
                        name_lines.append(word[:10] + "...")
                        current_line = ""
                    else:
                        name_lines.append(word)
                        current_line = ""

        if current_line:
            name_lines.append(current_line)

        # رسم اسم الشركة مع مسافات أفضل
        name_start_y = y + 15  # بداية صحيحة من أعلى الترويسة
        line_height = 16  # مسافة أكبر بين الأسطر

        for i, line in enumerate(name_lines):
            line_y = name_start_y + (i * line_height)
            painter.drawText(company_start_x, line_y, line)

        # مسافة بين اسم الشركة والتفاصيل
        details_gap = 8  # مسافة إضافية

        # تفاصيل الشركة مع خط أصغر للرول ومسافات أفضل
        painter.setFont(QFont("Arial", 9))  # خط أكبر قليلاً
        detail_height = 14  # مسافة أكبر بين التفاصيل
        current_y = name_start_y + (len(name_lines) * line_height) + details_gap

        # العنوان مع تقسيم ذكي للرول
        if company_address:
            # تقسيم العنوان إلى أسطر متعددة للرول
            address_words = company_address.split()
            address_lines = []
            current_address_line = ""

            for word in address_words:
                test_line = current_address_line + " " + word if current_address_line else word
                # حساب عرض النص مع الأيقونة
                test_with_icon = f"📍 {test_line}"
                font_metrics = painter.fontMetrics()
                text_width = font_metrics.width(test_with_icon)

                if text_width <= company_width - 5:  # هامش أمان
                    current_address_line = test_line
                else:
                    if current_address_line:
                        address_lines.append(current_address_line)
                        current_address_line = word
                    else:
                        # تقصير الكلمة الطويلة
                        if len(word) > 15:
                            address_lines.append(word[:12] + "...")
                            current_address_line = ""
                        else:
                            address_lines.append(word)
                            current_address_line = ""

            if current_address_line:
                address_lines.append(current_address_line)

            # رسم العنوان على أسطر متعددة
            for i, line in enumerate(address_lines):
                if i == 0:
                    address_text = f"📍 {line}"
                else:
                    address_text = f"   {line}"  # مسافة بدلاً من الأيقونة
                painter.drawText(company_start_x, current_y, address_text)
                current_y += detail_height  # مسافة بين الأسطر

            current_y += 2  # مسافة إضافية بعد العنوان

        # الهاتف مع مسافة
        if company_phone:
            phone_text = f"📞 {company_phone}"
            painter.drawText(company_start_x, current_y, phone_text)
            current_y += detail_height + 2  # مسافة إضافية

        # البريد الإلكتروني مختصر مع مسافة
        if company_email:
            # تقصير البريد للرول
            if len(company_email) > 18:
                short_email = company_email[:15] + "..."
            else:
                short_email = company_email
            email_text = f"📧 {short_email}"
            painter.drawText(company_start_x, current_y, email_text)
            current_y += detail_height + 2  # مسافة إضافية

        # الرقم الضريبي للرول مع مسافة
        try:
            tax_number = "310123456789003"

            tax_text = f"🏢 {tax_number}"
            painter.drawText(company_start_x, current_y, tax_text)

        except Exception as e:
            tax_text = "🏢 310..."
            painter.drawText(company_start_x, current_y, tax_text)

        # مسافة كبيرة بين الترويسة ومعلومات الفاتورة
        y += header_height + 20  # مسافة كافية بعد الترويسة

        # عنوان الفاتورة (مرتجع أو عادية)
        painter.setPen(QPen(QColor(44, 62, 80), 1))
        painter.setFont(QFont("Arial", 12, QFont.Bold))

        # عرض عنوان الفاتورة مع لون مميز للمرتجعات
        if self.invoice_data.get('is_return', False):
            if self.invoice_data.get('is_sale_return', False):
                painter.setPen(QPen(QColor(231, 76, 60), 2))  # أحمر للمرتجع
            else:
                painter.setPen(QPen(QColor(155, 89, 182), 2))  # بنفسجي للمرتجع

        painter.drawText(5, y, width - 10, 20, Qt.AlignCenter, self.invoice_data.get('invoice_title', 'فاتورة'))
        y += 30

        # رقم الفاتورة/المرتجع
        painter.setPen(QPen(QColor(44, 62, 80), 1))
        painter.setFont(QFont("Arial", 10, QFont.Bold))

        if self.invoice_data.get('is_return', False):
            painter.drawText(5, y, width - 10, 20, Qt.AlignCenter, f"رقم المرتجع: R{self.invoice_data['id']:06d}")
        else:
            painter.drawText(5, y, width - 10, 20, Qt.AlignCenter, f"رقم الفاتورة: #{self.invoice_data['id']:06d}")
        y += 25

        # رقم الفاتورة الأصلية للمرتجعات
        if self.invoice_data.get('is_return', False) and self.invoice_data.get('original_invoice_id'):
            painter.setPen(QPen(QColor(52, 152, 219), 1))
            painter.drawText(5, y, width - 10, 20, Qt.AlignCenter, f"الفاتورة الأصلية: #{self.invoice_data['original_invoice_id']:06d}")
            y += 25

        painter.setPen(QPen(QColor(44, 62, 80), 1))
        painter.drawText(5, y, width - 10, 20, Qt.AlignCenter, f"التاريخ: {self.invoice_data['date']}")
        y += 25

        # تحديد تسمية العميل/المورد
        contact_label = "العميل"
        if self.invoice_data.get('is_purchase_return', False) or self.invoice_data.get('invoice_type') == 'مشتريات':
            contact_label = "المورد"

        painter.drawText(5, y, width - 10, 20, Qt.AlignCenter, f"{contact_label}: {self.invoice_data['customer_name']}")
        y += 35

        # خط فاصل مع مسافات أكبر
        painter.setPen(QPen(QColor(189, 195, 199), 2))
        painter.drawLine(5, y, width - 5, y)
        y += 25  # مسافة أكبر بعد الخط الفاصل

        # المنتجات مع مسافات محسنة
        painter.setFont(QFont("Arial", 9))  # خط أكبر قليلاً
        painter.setPen(QPen(QColor(44, 62, 80), 1))

        for i, item in enumerate(self.invoice_data['items']):
            # اسم المنتج
            product_name = str(item.get('name', 'غير محدد'))
            # تقصير اسم المنتج إذا كان طويل للرول
            if len(product_name) > 25:
                product_name = product_name[:22] + "..."

            painter.drawText(5, y, product_name)
            y += 18  # مسافة أكبر بين اسم المنتج والتفاصيل

            # تفاصيل المنتج (الكمية والسعر)
            quantity = float(item.get('quantity', 0))
            price = float(item.get('price', 0))
            total = float(item.get('total', 0))
            painter.drawText(5, y, f"{quantity:,.0f} × {price:,.0f} = {total:,.0f} ج.م")
            y += 28  # مسافة أكبر بكثير بين المنتجات

            # مسافة إضافية كبيرة بين المنتجات (إلا للمنتج الأخير)
            if i < len(self.invoice_data['items']) - 1:
                y += 10  # مسافة أكبر

        # مسافة كبيرة قبل الخط الفاصل
        y += 20  # مسافة أكبر

        # خط فاصل
        painter.setPen(QPen(QColor(189, 195, 199), 2))
        painter.drawLine(5, y, width - 5, y)
        y += 25  # مسافة أكبر بعد الخط الفاصل

        # قسم تفاصيل الفاتورة للرول (بدون حدود)
        details_height = 120  # ارتفاع مناسب للرول

        # بدون خلفية أو حدود - فقط المحتوى

        # عنوان التفاصيل
        title_height = 25
        painter.setFont(QFont("Arial", 10, QFont.Bold))
        painter.setPen(QPen(QColor(0, 0, 0), 2))
        painter.drawText(5, y + 5, width - 10, title_height, Qt.AlignCenter, "📋 تفاصيل الفاتورة")

        # التفاصيل
        painter.setFont(QFont("Arial", 8))
        detail_y = y + title_height + 10

        # حساب المجموع الفرعي
        subtotal = sum(float(item.get('total', 0)) for item in self.invoice_data['items'])

        # حالة الفاتورة
        status = "مدفوعة" if self.invoice_data['remaining_amount'] == 0 else "جزئية" if self.invoice_data['paid_amount'] > 0 else "غير مدفوعة"
        status_icon = "✅" if status == "مدفوعة" else "⚠️" if status == "جزئية" else "❌"

        # عرض التفاصيل في سطور
        painter.drawText(10, detail_y, f"💰 المجموع الفرعي: {subtotal:,.0f} ج.م")
        detail_y += 15

        painter.drawText(10, detail_y, f"💳 المدفوع: {self.invoice_data['paid_amount']:,.0f} ج.م")
        detail_y += 15

        painter.drawText(10, detail_y, f"⏳ المتبقي: {self.invoice_data['remaining_amount']:,.0f} ج.م")
        detail_y += 15

        painter.drawText(10, detail_y, f"{status_icon} الحالة: {status}")
        detail_y += 15

        # الخصم إذا وجد
        if self.invoice_data['discount'] > 0:
            painter.drawText(10, detail_y, f"🎯 الخصم: {self.invoice_data['discount']:,.0f} ج.م")

        y += details_height + 15  # مسافة بعد قسم التفاصيل

        # الإجمالي النهائي بدون حدود للرول
        painter.setFont(QFont("Arial", 12, QFont.Bold))  # خط أكبر قليلاً
        painter.setPen(QPen(QColor(0, 0, 0), 2))  # نص أسود غامق

        # نص الإجمالي بدون خلفية أو حدود
        painter.drawText(5, y + 10, width - 10, 25, Qt.AlignCenter, f"💎 الإجمالي النهائي: {self.invoice_data['total_amount']:,.0f} ج.م")

        return y + 40  # مسافة إضافية في النهاية

    def save_as_pdf(self):
        """حفظ الفاتورة كملف PDF بجودة عالية"""
        try:
            from PyQt5.QtWidgets import QFileDialog

            filename, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ الفاتورة كـ PDF",
                f"فاتورة_{self.invoice_data['id']:06d}.pdf",
                "PDF Files (*.pdf)"
            )

            if filename:
                # إعداد الطابعة بدقة عالية
                printer = QPrinter(QPrinter.HighResolution)
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(filename)
                printer.setColorMode(QPrinter.Color)  # تفعيل الألوان

                if self.printer_type == "a4":
                    printer.setPageSize(QPrinter.A4)
                    printer.setPageMargins(10, 10, 10, 10, QPrinter.Millimeter)
                    # استخدام أبعاد ثابتة للحصول على نتيجة متسقة
                    pdf_width = 2480  # عرض A4 بدقة عالية
                    pdf_height = 3508  # ارتفاع A4 بدقة عالية
                else:
                    # إعداد حجم الرول بطريقة صحيحة
                    from PyQt5.QtCore import QSizeF
                    printer.setPageSize(QPrinter.Custom)
                    # تحديد حجم الورق بالمليمتر (80mm عرض، 200mm طول)
                    paper_size = QSizeF(80, 200)  # عرض × طول بالمليمتر
                    printer.setPaperSize(paper_size, QPrinter.Millimeter)
                    pdf_width = 945
                    pdf_height = 2362

                # إنشاء صورة بدقة عالية
                pixmap = QPixmap(pdf_width, pdf_height)
                pixmap.fill(QColor(255, 255, 255))  # خلفية بيضاء

                # رسم الفاتورة على الصورة
                painter = QPainter(pixmap)
                self.draw_invoice(painter, pdf_width, pdf_height)
                painter.end()

                # طباعة الصورة على PDF
                pdf_painter = QPainter()
                pdf_painter.begin(printer)

                # حساب نسبة التكبير للحفاظ على الجودة
                page_rect = printer.pageRect()
                scale_x = page_rect.width() / pdf_width
                scale_y = page_rect.height() / pdf_height
                scale = min(scale_x, scale_y)

                # رسم الصورة مع التكبير المناسب
                target_width = int(pdf_width * scale)
                target_height = int(pdf_height * scale)

                # توسيط الصورة
                x = (page_rect.width() - target_width) // 2
                y = (page_rect.height() - target_height) // 2

                pdf_painter.drawPixmap(x, y, target_width, target_height, pixmap)
                pdf_painter.end()

                QMessageBox.information(self, "نجح", f"تم حفظ الفاتورة بنجاح في:\n{filename}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ PDF:\n{str(e)}")

    def print_invoice(self):
        """طباعة الفاتورة بجودة عالية"""
        try:
            printer = QPrinter(QPrinter.HighResolution)
            printer.setColorMode(QPrinter.Color)  # تفعيل الألوان

            if self.printer_type == "a4":
                printer.setPageSize(QPrinter.A4)
                printer.setPageMargins(10, 10, 10, 10, QPrinter.Millimeter)
                print_width = 2480
                print_height = 3508
            else:
                # إعداد حجم الرول للطباعة بطريقة صحيحة
                from PyQt5.QtCore import QSizeF
                printer.setPageSize(QPrinter.Custom)
                # تحديد حجم الورق بالمليمتر (80mm عرض، 200mm طول)
                paper_size = QSizeF(80, 200)  # عرض × طول بالمليمتر
                printer.setPaperSize(paper_size, QPrinter.Millimeter)
                print_width = 945
                print_height = 2362

            print_dialog = QPrintDialog(printer, self)
            if print_dialog.exec_() == QPrintDialog.Accepted:
                # إنشاء صورة بدقة عالية للطباعة
                pixmap = QPixmap(print_width, print_height)
                pixmap.fill(QColor(255, 255, 255))

                # رسم الفاتورة على الصورة
                painter = QPainter(pixmap)
                self.draw_invoice(painter, print_width, print_height)
                painter.end()

                # طباعة الصورة
                print_painter = QPainter()
                print_painter.begin(printer)

                # حساب نسبة التكبير
                page_rect = printer.pageRect()
                scale_x = page_rect.width() / print_width
                scale_y = page_rect.height() / print_height
                scale = min(scale_x, scale_y)

                # رسم الصورة مع التكبير المناسب
                target_width = int(print_width * scale)
                target_height = int(print_height * scale)

                # توسيط الصورة
                x = (page_rect.width() - target_width) // 2
                y = (page_rect.height() - target_height) // 2

                print_painter.drawPixmap(x, y, target_width, target_height, pixmap)
                print_painter.end()

                QMessageBox.information(self, "نجح", "تم إرسال الفاتورة للطباعة بنجاح!")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الطباعة:\n{str(e)}")


def show_advanced_print_dialog(engine, invoice_id, parent=None):
    """عرض نافذة الطباعة المتقدمة"""
    dialog = AdvancedInvoicePrinter(engine, invoice_id, parent)
    dialog.exec_()
