('C:\\Users\\<USER>\\OneDrive\\Desktop\\Accounts_3\\build\\نظام_المحاسبة_العصري\\نظام_المحاسبة_العصري.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\Accounts_3\\build\\نظام_المحاسبة_العصري\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\Accounts_3\\build\\نظام_المحاسبة_العصري\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\Accounts_3\\build\\نظام_المحاسبة_العصري\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\Accounts_3\\build\\نظام_المحاسبة_العصري\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\Accounts_3\\build\\نظام_المحاسبة_العصري\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\Accounts_3\\build\\نظام_المحاسبة_العصري\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\Accounts_3\\venv311_new\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\Accounts_3\\venv311_new\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\Accounts_3\\venv311_new\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\Accounts_3\\venv311_new\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\Accounts_3\\venv311_new\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('pyi_rth_mplconfig',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\Accounts_3\\venv311_new\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_mplconfig.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\Accounts_3\\venv311_new\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('main',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\Accounts_3\\main.py',
   'PYSOURCE')],
 'python311.dll',
 True,
 False,
 False,
 [],
 None,
 None,
 None)
