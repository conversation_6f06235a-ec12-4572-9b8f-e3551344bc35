from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QLineEdit, QMessageBox, QFrame)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
import json
import os
from datetime import datetime

class ActivationDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.load_current_status()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("🔑 تفعيل البرنامج")
        self.setFixedSize(600, 400)
        self.setModal(True)
        
        # تطبيق التصميم
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #141E30, stop:1 #243B55);
                border-radius: 15px;
            }
            QFrame {
                background-color: rgba(255, 255, 255, 0.95);
                border-radius: 12px;
                padding: 20px;
            }
            QLabel {
                color: #2c3e50;
                font-size: 16px;
            }
            QLineEdit {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 10px;
                font-size: 16px;
                background: white;
                min-height: 20px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                min-width: 120px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2980b9, stop:1 #1f618d);
            }
            QPushButton:pressed {
                background: #1f618d;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # العنوان
        title_label = QLabel("🔑 تفعيل البرنامج")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: white;
            margin-bottom: 20px;
        """)
        layout.addWidget(title_label)
        
        # إطار المحتوى
        content_frame = QFrame()
        content_layout = QVBoxLayout(content_frame)
        
        # حالة التفعيل الحالية
        self.status_label = QLabel()
        self.status_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
        """)
        content_layout.addWidget(self.status_label)
        
        # إضافة معلومات الجهاز وكود العميل (تظهر إذا لم يتم التفعيل)
        self.info_label = QLabel()
        self.info_label.setWordWrap(True)
        self.info_label.setStyleSheet("color: #e67e22; font-size: 15px; margin-bottom: 10px;")
        self.info_label.setVisible(False)
        content_layout.addWidget(self.info_label)

        # زر نسخ بيانات العميل
        self.copy_btn = QPushButton("📋 نسخ بيانات العميل")
        self.copy_btn.clicked.connect(self.copy_customer_data)
        self.copy_btn.setVisible(False)
        content_layout.addWidget(self.copy_btn)
        
        # حقل كود التفعيل
        activation_layout = QHBoxLayout()
        activation_layout.addWidget(QLabel("كود التفعيل:"))
        self.activation_code = QLineEdit()
        self.activation_code.setPlaceholderText("أدخل كود التفعيل هنا...")
        activation_layout.addWidget(self.activation_code)
        content_layout.addLayout(activation_layout)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        activate_btn = QPushButton("✅ تفعيل")
        activate_btn.clicked.connect(self.activate_license)
        buttons_layout.addWidget(activate_btn)
        
        close_btn = QPushButton("❌ إغلاق")
        close_btn.clicked.connect(self.close)
        buttons_layout.addWidget(close_btn)
        
        content_layout.addLayout(buttons_layout)
        layout.addWidget(content_frame)
        
    def load_current_status(self):
        """تحميل حالة التفعيل الحالية"""
        try:
            settings_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), "company_settings.json")
            if os.path.exists(settings_file):
                with open(settings_file, "r", encoding="utf-8") as f:
                    settings = json.load(f)
                    
                if settings.get("activation_code"):
                    self.status_label.setText("✅ البرنامج مفعل")
                    self.status_label.setStyleSheet("""
                        font-size: 18px;
                        font-weight: bold;
                        color: #27ae60;
                        margin-bottom: 20px;
                    """)
                    self.info_label.setVisible(False)
                    self.copy_btn.setVisible(False)
                else:
                    self.status_label.setText("⚠️ البرنامج غير مفعل")
                    self.status_label.setStyleSheet("""
                        font-size: 18px;
                        font-weight: bold;
                        color: #e74c3c;
                        margin-bottom: 20px;
                    """)
                    # إظهار معلومات الجهاز وكود العميل
                    customer_code = self.get_customer_code()
                    machine_id = self.get_machine_id()
                    self.info_label.setText(f"<b>البرنامج غير مفعل!<br>يرجى إرسال كود العميل واسم الجهاز للمطور لتفعيل البرنامج.<br><br>🔑 كود العميل: <span style='color:#2980b9'>{customer_code}</span><br>💻 اسم الجهاز: <span style='color:#2980b9'>{machine_id}</span></b>")
                    self.info_label.setVisible(True)
                    self.copy_btn.setVisible(True)
            else:
                self.status_label.setText("⚠️ البرنامج غير مفعل")
        except Exception as e:
            self.status_label.setText("⚠️ خطأ في تحميل حالة التفعيل")
            
    def activate_license(self):
        """تفعيل البرنامج"""
        activation_code = self.activation_code.text().strip()
        
        if not activation_code:
            QMessageBox.warning(self, "تنبيه", "يرجى إدخال كود التفعيل")
            return
            
        try:
            settings_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), "company_settings.json")
            
            # قراءة الإعدادات الحالية
            if os.path.exists(settings_file):
                with open(settings_file, "r", encoding="utf-8") as f:
                    settings = json.load(f)
            else:
                settings = {}
            
            # تحديث كود التفعيل
            settings["activation_code"] = activation_code
            settings["activation_date"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            # حفظ الإعدادات
            with open(settings_file, "w", encoding="utf-8") as f:
                json.dump(settings, f, ensure_ascii=False, indent=4)
            
            QMessageBox.information(self, "تم التفعيل", "تم تفعيل البرنامج بنجاح!")
            self.load_current_status()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء التفعيل: {str(e)}") 

    def get_customer_code(self):
        # مولد كود العميل (مثال: من اسم الجهاز أو uuid)
        import uuid
        return str(uuid.uuid5(uuid.NAMESPACE_DNS, os.environ.get('COMPUTERNAME', 'client')))

    def get_machine_id(self):
        # اسم الجهاز
        return os.environ.get('COMPUTERNAME', 'client')

    def copy_customer_data(self):
        # نسخ كود العميل واسم الجهاز
        import pyperclip
        customer_code = self.get_customer_code()
        machine_id = self.get_machine_id()
        pyperclip.copy(f"كود العميل: {customer_code}\nاسم الجهاز: {machine_id}")
        QMessageBox.information(self, "تم النسخ", "تم نسخ بيانات العميل بنجاح!") 