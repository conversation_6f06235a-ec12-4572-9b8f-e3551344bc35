from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                             QPushButton, QLineEdit, QLabel, QTabWidget,
                             QTableWidgetItem, QMessageBox, QFrame, QGridLayout,
                             QHeaderView, QDialog, QComboBox, QFileDialog)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QColor, QFont
from sqlalchemy.orm import Session
from sqlalchemy import or_, desc
from database.models import Customer, Supplier
from utils.dialog_utils import setup_medium_dialog
from utils.theme_manager import theme_manager
import xlsxwriter
from datetime import datetime

class EditContactDialog(QDialog):
    def __init__(self, contact, is_customer=True):
        super().__init__()
        self.contact = contact
        self.is_customer = is_customer

        # إعداد النافذة مع خاصية التكبير
        contact_type = "العميل" if is_customer else "المورد"
        setup_medium_dialog(self, f"تعديل بيانات {contact_type}", 500, 400, 600, 500)

        self.setup_ui()
        
    def setup_ui(self):
        self.setWindowTitle(f"تعديل {'العميل' if self.is_customer else 'المورد'}")
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        form_layout = QGridLayout()
        
        self.name_input = QLineEdit(self.contact.name)
        self.phone_input = QLineEdit(self.contact.phone or "")
        self.address_input = QLineEdit(self.contact.address or "")
        
        form_layout.addWidget(QLabel("الاسم:"), 0, 0)
        form_layout.addWidget(self.name_input, 0, 1)
        form_layout.addWidget(QLabel("الهاتف:"), 1, 0)
        form_layout.addWidget(self.phone_input, 1, 1)
        form_layout.addWidget(QLabel("العنوان:"), 2, 0)
        form_layout.addWidget(self.address_input, 2, 1)
        
        layout.addLayout(form_layout)
        
        buttons = QHBoxLayout()
        save_btn = QPushButton("حفظ")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #28A745;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #6C757D;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #5A6268;
            }
        """)
        
        buttons.addStretch()
        buttons.addWidget(save_btn)
        buttons.addWidget(cancel_btn)
        
        layout.addLayout(buttons)
        
        save_btn.clicked.connect(self.accept)
        cancel_btn.clicked.connect(self.reject)

class ContactTab(QWidget):
    def __init__(self, engine, is_customer=True):
        super().__init__()
        self.engine = engine
        self.is_customer = is_customer
        self.setup_ui()
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self.perform_search)

    def is_dark_mode(self):
        """التحقق من الوضع الليلي"""
        if hasattr(self.parent(), 'is_dark_mode'):
            return self.parent().is_dark_mode
        return False

    def get_table_style(self):
        """الحصول على تنسيق الجدول حسب الوضع"""
        if self.is_dark_mode():
            # ألوان الوضع الليلي
            return """
                QTableWidget {
                    border: 2px solid #334155;
                    background-color: #0F172A;
                    gridline-color: #334155;
                    font-size: 16px;
                    color: #F8FAFC;
                    border-radius: 12px;
                }
                QTableWidget::item {
                    padding: 12px;
                    border-bottom: 1px solid #334155;
                    color: #F8FAFC;
                }
                QTableWidget::item:selected {
                    background-color: #6366F1;
                    color: #F8FAFC;
                }
                QTableWidget::item:hover {
                    background-color: rgba(99, 102, 241, 0.1);
                }
                QHeaderView::section {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #10B981, stop:1 #34D399);
                    color: #F8FAFC;
                    padding: 15px;
                    border: none;
                    font-weight: bold;
                    font-size: 18px;
                    border-radius: 8px;
                }
                QHeaderView::section:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #059669, stop:1 #10B981);
                }
            """
        else:
            # ألوان الوضع العادي
            return """
                QTableWidget {
                    border: 1px solid #DEE2E6;
                    background-color: white;
                    gridline-color: #DEE2E6;
                    font-size: 16px;
                    color: #212529;
                    border-radius: 8px;
                }
                QTableWidget::item {
                    padding: 12px;
                    border-bottom: 1px solid #DEE2E6;
                    color: #212529;
                }
                QTableWidget::item:selected {
                    background-color: #E3F2FD;
                    color: #1976D2;
                }
                QTableWidget::item:hover {
                    background-color: rgba(25, 118, 210, 0.1);
                }
                QHeaderView::section {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #28A745, stop:1 #58D68D);
                    color: white;
                    padding: 15px;
                    border: none;
                    font-weight: bold;
                    font-size: 18px;
                    border-radius: 8px;
                }
                QHeaderView::section:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #239B56, stop:1 #28A745);
                }
            """

    def get_row_colors(self, row):
        """الحصول على ألوان الصفوف حسب الوضع"""
        if self.is_dark_mode():
            return QColor("#1E293B") if row % 2 == 0 else QColor("#334155")
        else:
            return QColor("#F8F9FA") if row % 2 == 0 else QColor("#FFFFFF")

    def update_theme(self):
        """تحديث الألوان عند تغيير الوضع"""
        # تطبيق الثيم العام
        self.setStyleSheet(theme_manager.get_stylesheet("general"))

        # تحديث تنسيق الجدول
        self.contacts_table.setStyleSheet(theme_manager.get_stylesheet("table"))

        # تحديث الأزرار والحقول
        colors = theme_manager.get_colors()

        # تحديث إطار البحث
        if hasattr(self, 'search_input'):
            self.search_input.setStyleSheet(f"""
                QLineEdit {{
                    padding: 8px;
                    border: 1px solid {colors['border_color']};
                    border-radius: 4px;
                    min-width: 200px;
                    background-color: {colors['input_bg']};
                    color: {colors['input_text']};
                }}
                QLineEdit:focus {{
                    border-color: {colors['input_focus']};
                }}
            """)

        # تحديث القوائم المنسدلة
        combo_style = f"""
            QComboBox {{
                padding: 8px;
                border: 1px solid {colors['border_color']};
                border-radius: 4px;
                min-width: 150px;
                background-color: {colors['input_bg']};
                color: {colors['input_text']};
            }}
            QComboBox:focus {{
                border-color: {colors['input_focus']};
            }}
        """

        if hasattr(self, 'sort_by'):
            self.sort_by.setStyleSheet(combo_style)
        if hasattr(self, 'filter_balance'):
            self.filter_balance.setStyleSheet(combo_style)

        # إعادة تحميل البيانات لتطبيق الألوان الجديدة
        self.refresh_contacts()

    def setup_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # منطقة البحث والفلترة
        filters_frame = QFrame()
        filters_frame.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border-radius: 5px;
                padding: 10px;
            }
        """)
        filters_layout = QGridLayout()
        filters_frame.setLayout(filters_layout)
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("بحث...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #DEE2E6;
                border-radius: 4px;
                min-width: 200px;
            }
        """)
        
        self.sort_by = QComboBox()
        self.sort_by.addItems(["الكود", "الاسم", "الرصيد"])
        self.sort_by.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 1px solid #DEE2E6;
                border-radius: 4px;
                min-width: 150px;
            }
        """)
        
        self.filter_balance = QComboBox()
        self.filter_balance.addItems(["الكل", "عليه مديونية", "له رصيد", "رصيد صفري"])
        self.filter_balance.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 1px solid #DEE2E6;
                border-radius: 4px;
                min-width: 150px;
            }
        """)
        
        filters_layout.addWidget(QLabel("بحث:"), 0, 0)
        filters_layout.addWidget(self.search_input, 0, 1)
        filters_layout.addWidget(QLabel("ترتيب حسب:"), 0, 2)
        filters_layout.addWidget(self.sort_by, 0, 3)
        filters_layout.addWidget(QLabel("تصفية:"), 0, 4)
        filters_layout.addWidget(self.filter_balance, 0, 5)
        
        # أزرار الإضافة والتصدير
        buttons_layout = QHBoxLayout()
        add_btn = QPushButton(f"إضافة {'عميل' if self.is_customer else 'مورد'} جديد +")
        add_btn.setStyleSheet("""
            QPushButton {
                background-color: #28A745;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        
        export_btn = QPushButton("تصدير البيانات")
        export_btn.setStyleSheet("""
            QPushButton {
                background-color: #17A2B8;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        
        buttons_layout.addWidget(add_btn)
        buttons_layout.addWidget(export_btn)
        buttons_layout.addStretch()
        
        filters_layout.addLayout(buttons_layout, 1, 0, 1, 6)
        
        layout.addWidget(filters_frame)
        
        # نموذج الإضافة
        self.add_frame = QFrame()
        self.add_frame.setVisible(False)
        self.add_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #DEE2E6;
                border-radius: 5px;
                margin: 10px 0px;
                padding: 15px;
            }
        """)
        
        form_layout = QGridLayout()
        self.add_frame.setLayout(form_layout)
        
        # حقول النموذج
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("الاسم")
        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText("رقم الهاتف")
        self.address_input = QLineEdit()
        self.address_input.setPlaceholderText("العنوان")
        
        form_layout.addWidget(QLabel("الاسم:"), 0, 0)
        form_layout.addWidget(self.name_input, 0, 1)
        form_layout.addWidget(QLabel("الهاتف:"), 0, 2)
        form_layout.addWidget(self.phone_input, 0, 3)
        form_layout.addWidget(QLabel("العنوان:"), 1, 0)
        form_layout.addWidget(self.address_input, 1, 1, 1, 3)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        save_btn = QPushButton("حفظ")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #28A745;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #6C757D;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #5A6268;
            }
        """)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(cancel_btn)
        form_layout.addLayout(buttons_layout, 2, 0, 1, 4)
        
        save_btn.clicked.connect(self.save_contact)
        cancel_btn.clicked.connect(lambda: self.add_frame.setVisible(False))
        
        layout.addWidget(self.add_frame)
        
        # جدول جهات الاتصال
        self.contacts_table = QTableWidget()
        # تطبيق التنسيق حسب الوضع
        self.contacts_table.setStyleSheet(self.get_table_style())
        self.contacts_table.setColumnCount(6)
        self.contacts_table.setHorizontalHeaderLabels([
            "الكود", "الاسم", "الهاتف", "العنوان", "الرصيد", "تعديل"
        ])
        
        header = self.contacts_table.horizontalHeader()
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(3, QHeaderView.Stretch)
        for i in [0, 2, 4, 5]:
            header.setSectionResizeMode(i, QHeaderView.Fixed)
            header.resizeSection(i, 120)  # زيادة العرض لاستيعاب الخط الأكبر

        # تعديل ارتفاع الصفوف ليناسب الخط المحسن
        self.contacts_table.verticalHeader().setDefaultSectionSize(45)

        layout.addWidget(self.contacts_table)
        
        # تحميل البيانات
        self.refresh_contacts()
        
        # ربط الأحداث
        self.search_input.textChanged.connect(self.on_search_changed)
        self.sort_by.currentTextChanged.connect(self.refresh_contacts)
        self.filter_balance.currentTextChanged.connect(self.refresh_contacts)
        add_btn.clicked.connect(self.show_add_form)
        export_btn.clicked.connect(self.export_data)
        
    def show_add_form(self):
        self.add_frame.setVisible(True)
        self.name_input.setFocus()
        
    def on_search_changed(self):
        self.search_timer.start(300)
        
    def perform_search(self):
        search_text = self.search_input.text().strip()
        sort_column = self.sort_by.currentText()
        balance_filter = self.filter_balance.currentText()
        
        with Session(self.engine) as session:
            Model = Customer if self.is_customer else Supplier
            query = session.query(Model)
            
            # تطبيق البحث
            if search_text:
                query = query.filter(
                    or_(
                        Model.name.ilike(f"%{search_text}%"),
                        Model.phone.ilike(f"%{search_text}%"),
                        Model.id.in_([int(search_text)] if search_text.isdigit() else [])
                    )
                )
            
            # تطبيق فلتر الرصيد
            if balance_filter == "عليه مديونية":
                query = query.filter(Model.balance > 0)
            elif balance_filter == "له رصيد":
                query = query.filter(Model.balance < 0)
            elif balance_filter == "رصيد صفري":
                query = query.filter(Model.balance == 0)
            
            # تطبيق الترتيب
            if sort_column == "الكود":
                query = query.order_by(Model.id)
            elif sort_column == "الاسم":
                query = query.order_by(Model.name)
            elif sort_column == "الرصيد":
                query = query.order_by(desc(Model.balance))
            
            contacts = query.all()
            self.update_table(contacts)
            
    def save_contact(self):
        name = self.name_input.text()
        if not name:
            QMessageBox.warning(self, "خطأ", "يجب إدخال الاسم")
            return
            
        try:
            with Session(self.engine) as session:
                Model = Customer if self.is_customer else Supplier
                contact = Model(
                    name=name,
                    phone=self.phone_input.text(),
                    address=self.address_input.text()
                )
                session.add(contact)
                session.commit()
                
            self.refresh_contacts()
            self.clear_inputs()
            self.add_frame.setVisible(False)
            QMessageBox.information(self, "نجاح", f"تم إضافة {'العميل' if self.is_customer else 'المورد'} بنجاح")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الحفظ: {str(e)}")
            
    def refresh_contacts(self):
        try:
            with Session(self.engine) as session:
                Model = Customer if self.is_customer else Supplier
                contacts = session.query(Model).all()
                print(f"تم تحميل {len(contacts)} {'عميل' if self.is_customer else 'مورد'}")

                # إذا لم توجد موردين، أضف بعض الموردين التجريبيين
                if not self.is_customer and len(contacts) == 0:
                    self.add_sample_suppliers(session)
                    contacts = session.query(Model).all()
                    print(f"تم إضافة موردين تجريبيين. العدد الجديد: {len(contacts)}")

                self.update_table(contacts)
        except Exception as e:
            print(f"خطأ في تحميل {'العملاء' if self.is_customer else 'الموردين'}: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل البيانات: {str(e)}")
            # إنشاء جدول فارغ في حالة الخطأ
            self.contacts_table.setRowCount(0)

    def add_sample_suppliers(self, session):
        """إضافة موردين تجريبيين"""
        try:
            sample_suppliers = [
                {"name": "شركة الأهرام للتوريدات", "phone": "01234567890", "address": "القاهرة", "balance": 5000.0},
                {"name": "مؤسسة النيل التجارية", "phone": "01098765432", "address": "الجيزة", "balance": -2500.0},
                {"name": "شركة الدلتا للمواد", "phone": "01555123456", "address": "الإسكندرية", "balance": 0.0},
            ]

            for supplier_data in sample_suppliers:
                supplier = Supplier(
                    name=supplier_data["name"],
                    phone=supplier_data["phone"],
                    address=supplier_data["address"],
                    balance=supplier_data["balance"]
                )
                session.add(supplier)

            session.commit()
            print("تم إضافة موردين تجريبيين بنجاح")
        except Exception as e:
            print(f"خطأ في إضافة الموردين التجريبيين: {e}")
            session.rollback()
            
    def update_table(self, contacts):
        self.contacts_table.setRowCount(len(contacts))
        
        for row, contact in enumerate(contacts):
            bg_color = self.get_row_colors(row)
            
            self.contacts_table.setItem(row, 0, self.create_table_item(str(contact.id), bg_color))
            self.contacts_table.setItem(row, 1, self.create_table_item(contact.name, bg_color))
            self.contacts_table.setItem(row, 2, self.create_table_item(contact.phone or "", bg_color))
            self.contacts_table.setItem(row, 3, self.create_table_item(contact.address or "", bg_color))
            
            balance_item = self.create_table_item(f"{contact.balance:,.2f}", bg_color)
            if contact.balance > 0:
                balance_item.setForeground(QColor("#EF4444"))  # أحمر عصري
            elif contact.balance < 0:
                balance_item.setForeground(QColor("#10B981"))  # أخضر عصري
            self.contacts_table.setItem(row, 4, balance_item)
            
            # أزرار التحكم
            buttons_widget = QWidget()
            buttons_layout = QHBoxLayout()
            buttons_layout.setContentsMargins(5, 5, 5, 5)
            buttons_layout.setSpacing(5)

            edit_btn = QPushButton("✏️ تعديل")
            edit_btn.setStyleSheet("""
                QPushButton {
                    background-color: #FFC107;
                    border: none;
                    padding: 8px 12px;
                    color: black;
                    font-weight: bold;
                    border-radius: 5px;
                    min-width: 80px;
                    min-height: 35px;
                }
                QPushButton:hover {
                    background-color: #E0A800;
                }
            """)

            delete_btn = QPushButton("🗑️ حذف")
            delete_btn.setStyleSheet("""
                QPushButton {
                    background-color: #DC3545;
                    border: none;
                    padding: 8px 12px;
                    color: white;
                    font-weight: bold;
                    border-radius: 5px;
                    min-width: 80px;
                    min-height: 35px;
                }
                QPushButton:hover {
                    background-color: #C82333;
                }
            """)

            edit_btn.clicked.connect(lambda checked, c=contact: self.edit_contact(c))
            delete_btn.clicked.connect(lambda checked, c=contact: self.delete_contact(c))

            buttons_layout.addWidget(edit_btn)
            buttons_layout.addWidget(delete_btn)
            buttons_widget.setLayout(buttons_layout)
            
            self.contacts_table.setCellWidget(row, 5, buttons_widget)
            
    def create_table_item(self, text, bg_color, alignment=Qt.AlignCenter):
        item = QTableWidgetItem(str(text))
        item.setBackground(bg_color)
        item.setTextAlignment(alignment)
        return item
            
    def edit_contact(self, contact):
        dialog = EditContactDialog(contact, self.is_customer)
        if dialog.exec_() == QDialog.Accepted:
            try:
                with Session(self.engine) as session:
                    session.merge(contact)
                    contact.name = dialog.name_input.text()
                    contact.phone = dialog.phone_input.text()
                    contact.address = dialog.address_input.text()
                    session.commit()
                    
                self.refresh_contacts()
                QMessageBox.information(self, "نجاح", "تم تحديث البيانات بنجاح")
                
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء التحديث: {str(e)}")
    
    def delete_contact(self, contact):
        reply = QMessageBox.question(self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف {'العميل' if self.is_customer else 'المورد'}؟\n" +
            "سيتم حذف جميع المعاملات المرتبطة به",
            QMessageBox.Yes | QMessageBox.No)
            
        if reply == QMessageBox.Yes:
            try:
                with Session(self.engine) as session:
                    session.merge(contact)
                    session.delete(contact)
                    session.commit()
                    
                self.refresh_contacts()
                QMessageBox.information(self, "نجاح", "تم الحذف بنجاح")
                
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الحذف: {str(e)}")
    
    def export_data(self):
        try:
            file_name, _ = QFileDialog.getSaveFileName(
                self, "حفظ البيانات", "", "Excel Files (*.xlsx)"
            )
            
            if file_name:
                if not file_name.endswith('.xlsx'):
                    file_name += '.xlsx'
                    
                workbook = xlsxwriter.Workbook(file_name)
                worksheet = workbook.add_worksheet()
                
                # تنسيقات
                header_format = workbook.add_format({
                    'bold': True,
                    'align': 'center',
                    'valign': 'vcenter',
                    'bg_color': '#2C3E50',
                    'font_color': 'white',
                    'border': 1
                })
                
                cell_format = workbook.add_format({
                    'align': 'center',
                    'valign': 'vcenter',
                    'border': 1
                })
                
                # كتابة الترويسة
                headers = ["الكود", "الاسم", "الهاتف", "العنوان", "الرصيد"]
                for col, header in enumerate(headers):
                    worksheet.write(0, col, header, header_format)
                    worksheet.set_column(col, col, 15)
                
                # كتابة البيانات
                with Session(self.engine) as session:
                    Model = Customer if self.is_customer else Supplier
                    contacts = session.query(Model).all()
                    
                    for row, contact in enumerate(contacts, start=1):
                        worksheet.write(row, 0, contact.id, cell_format)
                        worksheet.write(row, 1, contact.name, cell_format)
                        worksheet.write(row, 2, contact.phone or "", cell_format)
                        worksheet.write(row, 3, contact.address or "", cell_format)
                        worksheet.write(row, 4, contact.balance, cell_format)
                
                workbook.close()
                QMessageBox.information(self, "نجاح", "تم تصدير البيانات بنجاح")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تصدير البيانات: {str(e)}")
    
    def clear_inputs(self):
        self.name_input.clear()
        self.phone_input.clear()
        self.address_input.clear()

class SupplierDialog(QDialog):
    """نافذة إضافة مورد جديد"""

    def __init__(self, engine, parent=None):
        super().__init__(parent)
        self.engine = engine
        self.saved_supplier_id = None  # لحفظ ID المورد الجديد

        # إعداد النافذة مع خاصية التكبير
        setup_medium_dialog(self, "إضافة مورد جديد", 500, 400, 600, 500)

        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)

        # عنوان النافذة
        title_label = QLabel("إضافة مورد جديد")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2C3E50;
                padding: 10px;
                background-color: #ECF0F1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # نموذج البيانات
        form_layout = QGridLayout()

        # حقل الاسم
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("اسم المورد")
        self.name_input.setStyleSheet("""
            QLineEdit {
                padding: 12px;
                border: 2px solid #BDC3C7;
                border-radius: 5px;
                font-size: 14px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3498DB;
            }
        """)

        # حقل الهاتف
        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText("رقم الهاتف")
        self.phone_input.setStyleSheet("""
            QLineEdit {
                padding: 12px;
                border: 2px solid #BDC3C7;
                border-radius: 5px;
                font-size: 14px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3498DB;
            }
        """)

        # حقل العنوان
        self.address_input = QLineEdit()
        self.address_input.setPlaceholderText("العنوان")
        self.address_input.setStyleSheet("""
            QLineEdit {
                padding: 12px;
                border: 2px solid #BDC3C7;
                border-radius: 5px;
                font-size: 14px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3498DB;
            }
        """)

        # حقل البريد الإلكتروني
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("البريد الإلكتروني (اختياري)")
        self.email_input.setStyleSheet("""
            QLineEdit {
                padding: 12px;
                border: 2px solid #BDC3C7;
                border-radius: 5px;
                font-size: 14px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3498DB;
            }
        """)

        # إضافة الحقول للنموذج
        form_layout.addWidget(QLabel("اسم المورد *:"), 0, 0)
        form_layout.addWidget(self.name_input, 0, 1)
        form_layout.addWidget(QLabel("رقم الهاتف:"), 1, 0)
        form_layout.addWidget(self.phone_input, 1, 1)
        form_layout.addWidget(QLabel("العنوان:"), 2, 0)
        form_layout.addWidget(self.address_input, 2, 1)
        form_layout.addWidget(QLabel("البريد الإلكتروني:"), 3, 0)
        form_layout.addWidget(self.email_input, 3, 1)

        layout.addLayout(form_layout)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        save_btn = QPushButton("💾 حفظ")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #27AE60;
                color: white;
                font-size: 16px;
                font-weight: bold;
                min-width: 120px;
                padding: 12px 20px;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1E8449;
            }
        """)

        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #95A5A6;
                color: white;
                font-size: 16px;
                font-weight: bold;
                min-width: 120px;
                padding: 12px 20px;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #7F8C8D;
            }
            QPushButton:pressed {
                background-color: #6C7B7D;
            }
        """)

        buttons_layout.addStretch()
        buttons_layout.addWidget(cancel_btn)
        buttons_layout.addWidget(save_btn)

        layout.addLayout(buttons_layout)

        # ربط الأحداث
        save_btn.clicked.connect(self.save_supplier)
        cancel_btn.clicked.connect(self.reject)

        # تركيز على حقل الاسم
        self.name_input.setFocus()

    def save_supplier(self):
        """حفظ المورد الجديد"""
        name = self.name_input.text().strip()
        if not name:
            QMessageBox.warning(self, "تنبيه", "يرجى إدخال اسم المورد")
            self.name_input.setFocus()
            return

        try:
            with Session(self.engine) as session:
                # التحقق من عدم وجود مورد بنفس الاسم
                existing = session.query(Supplier).filter(Supplier.name == name).first()
                if existing:
                    QMessageBox.warning(self, "تنبيه", f"يوجد مورد بالاسم '{name}' مسبقاً")
                    self.name_input.setFocus()
                    return

                # إنشاء المورد الجديد
                new_supplier = Supplier(
                    name=name,
                    phone=self.phone_input.text().strip() or None,
                    address=self.address_input.text().strip() or None,
                    email=self.email_input.text().strip() or None,
                    balance=0.0,
                    is_active=True
                )

                session.add(new_supplier)
                session.commit()

                # حفظ ID المورد الجديد
                self.saved_supplier_id = new_supplier.id

                QMessageBox.information(self, "✅ نجح الحفظ", f"تم إضافة المورد '{name}' بنجاح!")
                self.accept()

        except Exception as e:
            QMessageBox.critical(self, "❌ خطأ", f"حدث خطأ أثناء حفظ المورد:\n{str(e)}")
            print(f"Error saving supplier: {e}")

class SupplierDialog(QDialog):
    """نافذة إضافة مورد جديد"""

    def __init__(self, engine, parent=None):
        super().__init__(parent)
        self.engine = engine
        self.saved_supplier_id = None  # لحفظ ID المورد الجديد

        # إعداد النافذة مع خاصية التكبير
        setup_medium_dialog(self, "إضافة مورد جديد", 500, 400, 600, 500)

        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)

        # عنوان النافذة
        title_label = QLabel("إضافة مورد جديد")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2C3E50;
                padding: 10px;
                background-color: #ECF0F1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # نموذج البيانات
        form_layout = QGridLayout()

        # حقل الاسم
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("اسم المورد")
        self.name_input.setStyleSheet("""
            QLineEdit {
                padding: 12px;
                border: 2px solid #BDC3C7;
                border-radius: 5px;
                font-size: 14px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3498DB;
            }
        """)

        # حقل الهاتف
        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText("رقم الهاتف")
        self.phone_input.setStyleSheet("""
            QLineEdit {
                padding: 12px;
                border: 2px solid #BDC3C7;
                border-radius: 5px;
                font-size: 14px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3498DB;
            }
        """)

        # حقل العنوان
        self.address_input = QLineEdit()
        self.address_input.setPlaceholderText("العنوان")
        self.address_input.setStyleSheet("""
            QLineEdit {
                padding: 12px;
                border: 2px solid #BDC3C7;
                border-radius: 5px;
                font-size: 14px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3498DB;
            }
        """)

        # حقل البريد الإلكتروني
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("البريد الإلكتروني (اختياري)")
        self.email_input.setStyleSheet("""
            QLineEdit {
                padding: 12px;
                border: 2px solid #BDC3C7;
                border-radius: 5px;
                font-size: 14px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3498DB;
            }
        """)

        # إضافة الحقول للنموذج
        form_layout.addWidget(QLabel("اسم المورد *:"), 0, 0)
        form_layout.addWidget(self.name_input, 0, 1)
        form_layout.addWidget(QLabel("رقم الهاتف:"), 1, 0)
        form_layout.addWidget(self.phone_input, 1, 1)
        form_layout.addWidget(QLabel("العنوان:"), 2, 0)
        form_layout.addWidget(self.address_input, 2, 1)
        form_layout.addWidget(QLabel("البريد الإلكتروني:"), 3, 0)
        form_layout.addWidget(self.email_input, 3, 1)

        layout.addLayout(form_layout)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        save_btn = QPushButton("💾 حفظ")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #27AE60;
                color: white;
                font-size: 16px;
                font-weight: bold;
                min-width: 120px;
                padding: 12px 20px;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1E8449;
            }
        """)

        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #95A5A6;
                color: white;
                font-size: 16px;
                font-weight: bold;
                min-width: 120px;
                padding: 12px 20px;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #7F8C8D;
            }
            QPushButton:pressed {
                background-color: #6C7B7D;
            }
        """)

        buttons_layout.addStretch()
        buttons_layout.addWidget(cancel_btn)
        buttons_layout.addWidget(save_btn)

        layout.addLayout(buttons_layout)

        # ربط الأحداث
        save_btn.clicked.connect(self.save_supplier)
        cancel_btn.clicked.connect(self.reject)

        # تركيز على حقل الاسم
        self.name_input.setFocus()

    def save_supplier(self):
        """حفظ المورد الجديد"""
        name = self.name_input.text().strip()
        if not name:
            QMessageBox.warning(self, "تنبيه", "يرجى إدخال اسم المورد")
            self.name_input.setFocus()
            return

        try:
            with Session(self.engine) as session:
                # التحقق من عدم وجود مورد بنفس الاسم
                existing = session.query(Supplier).filter(Supplier.name == name).first()
                if existing:
                    QMessageBox.warning(self, "تنبيه", f"يوجد مورد بالاسم '{name}' مسبقاً")
                    self.name_input.setFocus()
                    return

                # إنشاء المورد الجديد
                new_supplier = Supplier(
                    name=name,
                    phone=self.phone_input.text().strip() or None,
                    address=self.address_input.text().strip() or None,
                    email=self.email_input.text().strip() or None,
                    balance=0.0
                )

                session.add(new_supplier)
                session.commit()

                # حفظ ID المورد الجديد
                self.saved_supplier_id = new_supplier.id

                QMessageBox.information(self, "✅ نجح الحفظ", f"تم إضافة المورد '{name}' بنجاح!")
                self.accept()

        except Exception as e:
            QMessageBox.critical(self, "❌ خطأ", f"حدث خطأ أثناء حفظ المورد:\n{str(e)}")
            print(f"Error saving supplier: {e}")

class ContactsWidget(QWidget):
    def __init__(self, engine):
        super().__init__()
        self.engine = engine
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # إنشاء تبويبات للعملاء والموردين
        self.tab_widget = QTabWidget()  # Changed this line to save the reference
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #334155;
                border-radius: 8px;
                background: #0F172A;
            }
            QTabBar::tab {
                background: #1E293B;
                border: 2px solid #334155;
                padding: 12px 40px;
                margin-right: 4px;
                border-radius: 8px 8px 0px 0px;
                color: #CBD5E1;
                font-size: 16px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #6366F1, stop:1 #8B5CF6);
                border-bottom-color: #0F172A;
                color: #F8FAFC;
            }
            QTabBar::tab:hover {
                background: #334155;
                color: #F8FAFC;
            }
        """)
        self.customers_tab = ContactTab(self.engine, True)
        self.suppliers_tab = ContactTab(self.engine, False)

        self.tab_widget.addTab(self.customers_tab, "العملاء")
        self.tab_widget.addTab(self.suppliers_tab, "الموردين")
        layout.addWidget(self.tab_widget)

    def update_theme(self):
        """تحديث ألوان الثيم"""
        # تطبيق الثيم العام
        self.setStyleSheet(theme_manager.get_stylesheet("general"))

        # تحديث التبويبات
        colors = theme_manager.get_colors()
        self.tab_widget.setStyleSheet(f"""
            QTabWidget::pane {{
                border: 2px solid {colors['border_color']};
                background-color: {colors['card_bg']};
                border-radius: 8px;
            }}
            QTabBar::tab {{
                background-color: {colors['secondary_bg']};
                color: {colors['primary_text']};
                padding: 12px 24px;
                margin: 2px;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
            }}
            QTabBar::tab:selected {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {colors['primary']}, stop:1 #34D399);
                color: white;
            }}
            QTabBar::tab:hover {{
                background-color: {colors['button_hover']};
            }}
        """)

        # تحديث التبويبات الفرعية
        if hasattr(self, 'customers_tab'):
            self.customers_tab.update_theme()
        if hasattr(self, 'suppliers_tab'):
            self.suppliers_tab.update_theme()