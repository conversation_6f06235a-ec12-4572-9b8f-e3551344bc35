from sqlalchemy import <PERSON>um<PERSON>, Inte<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Foreign<PERSON><PERSON>, Table, DateTime
from sqlalchemy.orm import relationship
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
from database.models import Base

# جدول العلاقة بين المستخدمين والأدوار
user_roles = Table('user_roles', Base.metadata,
    Column('user_id', Integer, ForeignKey('users.id')),
    Column('role_id', Integer, ForeignKey('roles.id'))
)

class User(Base):
    __tablename__ = 'users'

    id = Column(Integer, primary_key=True)
    username = Column(String(50), unique=True, nullable=False)
    password_hash = Column(String(256), nullable=False)
    full_name = Column(String(100))
    email = Column(String(100))
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.now)
    last_login = Column(DateTime)

    roles = relationship('Role', secondary=user_roles, backref='users')
    audit_logs = relationship('AuditLog', backref='user')

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class Role(Base):
    __tablename__ = 'roles'

    id = Column(Integer, primary_key=True)
    name = Column(String(50), unique=True, nullable=False)
    description = Column(String(200))
    permissions = relationship('Permission', secondary='role_permissions')

class Permission(Base):
    __tablename__ = 'permissions'

    id = Column(Integer, primary_key=True)
    name = Column(String(50), unique=True, nullable=False)
    description = Column(String(200))

class RolePermission(Base):
    __tablename__ = 'role_permissions'

    role_id = Column(Integer, ForeignKey('roles.id'), primary_key=True)
    permission_id = Column(Integer, ForeignKey('permissions.id'), primary_key=True)

class AuditLog(Base):
    __tablename__ = 'audit_logs'

    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'))
    action = Column(String(50), nullable=False)
    table_name = Column(String(50))
    record_id = Column(Integer)
    details = Column(String(500))
    timestamp = Column(DateTime, default=datetime.now)

# تحديث دالة init_db لإضافة البيانات الأولية
def init_db(engine):
    Base.metadata.create_all(engine)
    
    # إضافة الأدوار والصلاحيات الأساسية
    from sqlalchemy.orm import Session
    with Session(engine) as session:
        # التحقق من وجود الأدوار
        if not session.query(Role).first():
            # إنشاء الأدوار الأساسية
            admin_role = Role(name="مدير", description="صلاحيات كاملة للنظام")
            accountant_role = Role(name="محاسب", description="صلاحيات المحاسبة والتقارير")
            cashier_role = Role(name="كاشير", description="صلاحيات المبيعات والمشتريات")
            
            # إنشاء الصلاحيات
            permissions = [
                Permission(name="إدارة_المستخدمين", description="إدارة حسابات المستخدمين"),
                Permission(name="إدارة_المبيعات", description="إدارة عمليات البيع"),
                Permission(name="إدارة_المشتريات", description="إدارة عمليات الشراء"),
                Permission(name="إدارة_المخزون", description="إدارة المخزون"),
                Permission(name="إدارة_العملاء", description="إدارة العملاء"),
                Permission(name="إدارة_الموردين", description="إدارة الموردين"),
                Permission(name="التقارير", description="عرض وطباعة التقارير"),
                Permission(name="النسخ_الاحتياطي", description="إدارة النسخ الاحتياطي"),
            ]
            
            session.add_all(permissions)
            session.flush()
            
            # إضافة الصلاحيات للأدوار
            admin_role.permissions = permissions
            accountant_role.permissions = [p for p in permissions if p.name in [
                "إدارة_المبيعات", "إدارة_المشتريات", "التقارير"
            ]]
            cashier_role.permissions = [p for p in permissions if p.name in [
                "إدارة_المبيعات"
            ]]
            
            session.add_all([admin_role, accountant_role, cashier_role])
            
            # إنشاء حساب المدير الافتراضي
            admin_user = User(
                username="sicoo",
                full_name="مدير النظام",
                email="<EMAIL>",
            )
            admin_user.set_password("sicoo123")
            admin_user.roles = [admin_role]
            
            session.add(admin_user)
            session.commit()